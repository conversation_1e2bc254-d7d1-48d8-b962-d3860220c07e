{% extends "base.html" %}

{% block title %}إدارة عقود الإيجار المحسنة{% endblock %}

{% block extra_css %}
<style>
    /* تصميم البطاقات الاحترافي لعقود الإيجار */
    .lease-card {
        border: none;
        border-radius: 20px;
        box-shadow: 0 8px 32px rgba(0,0,0,0.12);
        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        overflow: hidden;
        background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
        position: relative;
        margin-bottom: 30px;
    }

    .lease-card:hover {
        transform: translateY(-8px) scale(1.02);
        box-shadow: 0 20px 40px rgba(0,0,0,0.2);
    }

    .lease-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    }

    .lease-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 30px 25px;
        position: relative;
        text-align: center;
    }

    .lease-icon {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        background: rgba(255,255,255,0.2);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 2rem;
        margin: 0 auto 20px;
        border: 4px solid rgba(255,255,255,0.3);
        backdrop-filter: blur(10px);
        transition: all 0.3s ease;
    }

    .lease-card:hover .lease-icon {
        transform: scale(1.1);
        border-color: rgba(255,255,255,0.6);
    }

    .lease-code {
        font-size: 1.4rem;
        font-weight: 700;
        margin-bottom: 8px;
        text-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .lease-property {
        opacity: 0.9;
        font-size: 1rem;
        font-weight: 500;
    }

    .lease-status {
        position: absolute;
        top: 20px;
        right: 20px;
        padding: 8px 16px;
        border-radius: 25px;
        font-size: 0.75rem;
        font-weight: 700;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255,255,255,0.2);
    }

    .status-active {
        background: linear-gradient(135deg, #28a745, #20c997);
        color: white;
        box-shadow: 0 4px 15px rgba(40, 167, 69, 0.4);
    }

    .status-expired {
        background: linear-gradient(135deg, #dc3545, #fd7e14);
        color: white;
        box-shadow: 0 4px 15px rgba(220, 53, 69, 0.4);
    }

    .status-pending {
        background: linear-gradient(135deg, #ffc107, #fd7e14);
        color: white;
        box-shadow: 0 4px 15px rgba(255, 193, 7, 0.4);
    }

    .status-terminated {
        background: linear-gradient(135deg, #6c757d, #adb5bd);
        color: white;
        box-shadow: 0 4px 15px rgba(108, 117, 125, 0.4);
    }

    .lease-info {
        padding: 25px;
    }

    .info-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
        padding: 12px 0;
        border-bottom: 2px solid #f8f9fa;
        transition: all 0.3s ease;
    }

    .info-row:hover {
        background: rgba(102, 126, 234, 0.05);
        border-radius: 8px;
        padding-left: 10px;
        padding-right: 10px;
    }

    .info-row:last-child {
        border-bottom: none;
        margin-bottom: 0;
    }

    .info-label {
        color: #34495e;
        font-size: 0.95rem;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .info-value {
        font-weight: 600;
        color: #2c3e50;
        font-size: 0.95rem;
    }
    
    /* شريط التقدم للعقد */
    .lease-progress {
        margin: 20px 0;
        padding: 15px;
        background: rgba(102, 126, 234, 0.05);
        border-radius: 12px;
    }

    .progress-label {
        display: flex;
        justify-content: space-between;
        margin-bottom: 8px;
        font-size: 0.9rem;
        color: #34495e;
        font-weight: 600;
    }

    .progress {
        height: 12px;
        border-radius: 10px;
        background: #e9ecef;
        box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
        overflow: hidden;
    }

    .progress-bar {
        border-radius: 10px;
        transition: width 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        position: relative;
    }

    .progress-bar::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
        animation: shimmer 2s infinite;
    }

    @keyframes shimmer {
        0% { transform: translateX(-100%); }
        100% { transform: translateX(100%); }
    }

    .progress-active { background: linear-gradient(90deg, #28a745, #20c997); }
    .progress-warning { background: linear-gradient(90deg, #ffc107, #fd7e14); }
    .progress-danger { background: linear-gradient(90deg, #dc3545, #fd7e14); }

    /* أزرار الإجراءات */
    .lease-actions {
        padding: 20px 25px;
        background: linear-gradient(145deg, #f8f9fa 0%, #e9ecef 100%);
        display: flex;
        gap: 12px;
        justify-content: center;
        border-top: 2px solid #e9ecef;
    }

    .btn-action {
        flex: 1;
        padding: 12px 16px;
        border-radius: 12px;
        font-size: 0.9rem;
        font-weight: 600;
        transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        border: none;
        text-decoration: none;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        position: relative;
        overflow: hidden;
    }

    .btn-action::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: left 0.5s;
    }

    .btn-action:hover::before {
        left: 100%;
    }

    .btn-view {
        background: linear-gradient(135deg, #3498db, #2980b9);
        color: white;
        box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
    }

    .btn-view:hover {
        background: linear-gradient(135deg, #2980b9, #1f4e79);
        color: white;
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(52, 152, 219, 0.4);
    }

    .btn-edit {
        background: linear-gradient(135deg, #f39c12, #e67e22);
        color: white;
        box-shadow: 0 4px 15px rgba(243, 156, 18, 0.3);
    }

    .btn-edit:hover {
        background: linear-gradient(135deg, #e67e22, #d35400);
        color: white;
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(243, 156, 18, 0.4);
    }

    .btn-renew {
        background: linear-gradient(135deg, #28a745, #20c997);
        color: white;
        box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
    }

    .btn-renew:hover {
        background: linear-gradient(135deg, #20c997, #17a2b8);
        color: white;
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(40, 167, 69, 0.4);
    }

    /* تصميم الصفحة الرئيسي */
    .page-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 40px 0;
        margin: -20px -20px 40px -20px;
        border-radius: 0 0 30px 30px;
        position: relative;
        overflow: hidden;
    }

    .page-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="90" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        opacity: 0.3;
    }

    .page-title {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 10px;
        text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        position: relative;
        z-index: 1;
    }

    .page-subtitle {
        font-size: 1.1rem;
        opacity: 0.9;
        position: relative;
        z-index: 1;
    }

    /* بطاقات الإحصائيات */
    .stats-cards {
        margin-bottom: 40px;
    }

    .stat-card {
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        border: none;
        border-radius: 20px;
        padding: 30px 25px;
        text-align: center;
        box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        position: relative;
        overflow: hidden;
        margin-bottom: 20px;
    }

    .stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    }

    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 40px rgba(0,0,0,0.15);
    }

    .stat-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        margin: 0 auto 20px;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    }

    .stat-number {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 8px;
        color: #2c3e50;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .stat-label {
        font-size: 1rem;
        color: #6c757d;
        font-weight: 600;
    }

    /* فلاتر البحث */
    .search-filters {
        background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
        border: none;
        border-radius: 20px;
        padding: 30px;
        margin-bottom: 40px;
        box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        position: relative;
    }

    .search-filters::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        border-radius: 20px 20px 0 0;
    }

    .filter-title {
        font-size: 1.3rem;
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .form-control, .form-select {
        border: 2px solid #e9ecef;
        border-radius: 12px;
        padding: 12px 16px;
        font-size: 0.95rem;
        transition: all 0.3s ease;
        background: white;
    }

    .form-control:focus, .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        background: white;
    }

    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 12px;
        padding: 12px 30px;
        font-weight: 600;
        transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    }

    .btn-primary:hover {
        background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    }

    /* زر الإضافة العائم */
    .floating-add-btn {
        position: fixed;
        bottom: 30px;
        right: 30px;
        width: 70px;
        height: 70px;
        border-radius: 50%;
        background: linear-gradient(135deg, #28a745, #20c997);
        border: none;
        color: white;
        font-size: 1.8rem;
        box-shadow: 0 8px 25px rgba(40, 167, 69, 0.4);
        transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        z-index: 1000;
        cursor: pointer;
    }

    .floating-add-btn:hover {
        transform: scale(1.15) rotate(90deg);
        box-shadow: 0 12px 35px rgba(40, 167, 69, 0.6);
    }

    /* حالة فارغة */
    .empty-state {
        text-align: center;
        padding: 80px 20px;
        background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
        border-radius: 20px;
        margin: 40px 0;
        box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    }

    .empty-state i {
        font-size: 5rem;
        margin-bottom: 30px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .empty-state h3 {
        color: #2c3e50;
        font-weight: 700;
        margin-bottom: 15px;
    }

    .empty-state p {
        color: #6c757d;
        font-size: 1.1rem;
        margin-bottom: 30px;
    }

    /* مؤشرات التحذير والدفع */
    .expiry-warning {
        background: linear-gradient(135deg, #ffc107, #fd7e14);
        color: white;
        padding: 6px 12px;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: 600;
        margin-right: 8px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        box-shadow: 0 2px 8px rgba(255, 193, 7, 0.3);
    }

    .payment-indicator {
        display: inline-flex;
        align-items: center;
        gap: 8px;
        padding: 6px 12px;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .payment-current {
        background: linear-gradient(135deg, rgba(40, 167, 69, 0.15), rgba(32, 201, 151, 0.15));
        color: #28a745;
        border: 2px solid rgba(40, 167, 69, 0.3);
        box-shadow: 0 2px 8px rgba(40, 167, 69, 0.2);
    }

    .payment-late {
        background: linear-gradient(135deg, rgba(220, 53, 69, 0.15), rgba(253, 126, 20, 0.15));
        color: #dc3545;
        border: 2px solid rgba(220, 53, 69, 0.3);
        box-shadow: 0 2px 8px rgba(220, 53, 69, 0.2);
    }

    .payment-upcoming {
        background: linear-gradient(135deg, rgba(255, 193, 7, 0.15), rgba(253, 126, 20, 0.15));
        color: #ffc107;
        border: 2px solid rgba(255, 193, 7, 0.3);
        box-shadow: 0 2px 8px rgba(255, 193, 7, 0.2);
    }

    /* إحصائيات سريعة */
    .quick-stats {
        display: flex;
        gap: 20px;
        margin-top: 20px;
        padding-top: 20px;
        border-top: 2px solid #f8f9fa;
    }

    .quick-stat {
        text-align: center;
        flex: 1;
        padding: 15px;
        border-radius: 12px;
        background: rgba(102, 126, 234, 0.05);
        transition: all 0.3s ease;
    }

    .quick-stat:hover {
        background: rgba(102, 126, 234, 0.1);
        transform: translateY(-3px);
    }

    .quick-stat-number {
        font-size: 1.5rem;
        font-weight: 700;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .quick-stat-label {
        font-size: 0.9rem;
        color: #6c757d;
        margin-top: 5px;
        font-weight: 600;
    }

    /* معلومات المستأجر */
    .tenant-info {
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
        padding: 15px;
        border-radius: 12px;
        margin-bottom: 20px;
        border: 2px solid rgba(102, 126, 234, 0.2);
    }

    .tenant-name {
        font-weight: 700;
        color: #667eea;
        margin-bottom: 8px;
        font-size: 1.1rem;
    }

    .tenant-contact {
        font-size: 0.95rem;
        color: #6c757d;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    /* ملخص مالي */
    .financial-summary {
        background: linear-gradient(135deg, rgba(40, 167, 69, 0.1), rgba(32, 201, 151, 0.1));
        padding: 15px;
        border-radius: 12px;
        margin-bottom: 20px;
        border: 2px solid rgba(40, 167, 69, 0.2);
    }

    .financial-amount {
        font-size: 1.3rem;
        font-weight: 700;
        color: #28a745;
        margin-bottom: 5px;
    }

    .financial-label {
        font-size: 0.9rem;
        color: #6c757d;
        font-weight: 600;
    }

    /* تحسينات إضافية */
    .animate-fade-in {
        animation: fadeIn 0.6s ease-out;
    }

    @keyframes fadeIn {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .loading-skeleton {
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: loading 1.5s infinite;
    }

    @keyframes loading {
        0% {
            background-position: 200% 0;
        }
        100% {
            background-position: -200% 0;
        }
    }
    
    .monthly-rent {
        font-size: 1.3rem;
        font-weight: 700;
        color: #28a745;
        margin-bottom: 8px;
    }

    .total-value {
        font-size: 1rem;
        color: #6c757d;
        font-weight: 600;
    }
</style>
{% endblock %}

{% block content %}
<div class="page-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="page-title">
                    <i class="fas fa-file-contract me-3"></i>
                    إدارة عقود الإيجار المحسنة
                </h1>
                <p class="page-subtitle">إدارة شاملة ومتطورة لجميع عقود الإيجار والمستأجرين</p>
            </div>
            <div class="col-md-4 text-end">
                <button class="btn btn-light btn-lg shadow-lg" data-bs-toggle="modal" data-bs-target="#addLeaseModal">
                    <i class="fas fa-plus me-2"></i>
                    إضافة عقد جديد
                </button>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <!-- إحصائيات سريعة -->
    <div class="row stats-cards">
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-file-contract"></i>
                </div>
                <div class="stat-number">{{ total_leases or 0 }}</div>
                <div class="stat-label">إجمالي العقود</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon" style="background: linear-gradient(135deg, #28a745, #20c997);">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="stat-number">{{ active_leases or 0 }}</div>
                <div class="stat-label">عقود نشطة</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon" style="background: linear-gradient(135deg, #ffc107, #fd7e14);">
                    <i class="fas fa-clock"></i>
                </div>>
                <div class="stat-number">{{ expiring_soon or 0 }}</div>
                <div class="stat-label">تنتهي قريباً</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon" style="background: linear-gradient(135deg, #dc3545, #fd7e14);">
                    <i class="fas fa-times-circle"></i>
                </div>
                <div class="stat-number">{{ expired_leases or 0 }}</div>
                <div class="stat-label">عقود منتهية</div>
            </div>
        </div>
    </div>

    <!-- فلاتر البحث -->
    <div class="search-filters">
        <h5 class="filter-title">
            <i class="fas fa-filter"></i>
            فلاتر البحث والتصفية
        </h5>
        <form method="GET" id="filterForm">
            <div class="row">
                <div class="col-md-3">
                    <label class="form-label">البحث العام</label>
                    <input type="text" class="form-control" name="search" value="{{ request.args.get('search', '') }}"
                           placeholder="رمز العقد، العقار، أو المستأجر">
                </div>
                <div class="col-md-2">
                    <label class="form-label">حالة العقد</label>
                    <select class="form-select" name="status">
                        <option value="">جميع الحالات</option>
                        <option value="نشط" {{ 'selected' if request.args.get('status') == 'نشط' }}>نشط</option>
                        <option value="منتهي" {{ 'selected' if request.args.get('status') == 'منتهي' }}>منتهي</option>
                        <option value="في الانتظار" {{ 'selected' if request.args.get('status') == 'في الانتظار' }}>في الانتظار</option>
                        <option value="ملغي" {{ 'selected' if request.args.get('status') == 'ملغي' }}>ملغي</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">نوع العقار</label>
                    <select class="form-select" name="property_type">
                        <option value="">جميع الأنواع</option>
                        <option value="شقة" {{ 'selected' if request.args.get('property_type') == 'شقة' }}>شقة</option>
                        <option value="فيلا" {{ 'selected' if request.args.get('property_type') == 'فيلا' }}>فيلا</option>
                        <option value="مكتب" {{ 'selected' if request.args.get('property_type') == 'مكتب' }}>مكتب</option>
                        <option value="محل" {{ 'selected' if request.args.get('property_type') == 'محل' }}>محل</option>
                        <option value="مستودع" {{ 'selected' if request.args.get('property_type') == 'مستودع' }}>مستودع</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">فترة الانتهاء</label>
                    <select class="form-select" name="expiry_period">
                        <option value="">جميع الفترات</option>
                        <option value="week" {{ 'selected' if request.args.get('expiry_period') == 'week' }}>خلال أسبوع</option>
                        <option value="month" {{ 'selected' if request.args.get('expiry_period') == 'month' }}>خلال شهر</option>
                        <option value="quarter" {{ 'selected' if request.args.get('expiry_period') == 'quarter' }}>خلال 3 أشهر</option>
                        <option value="year" {{ 'selected' if request.args.get('expiry_period') == 'year' }}>خلال سنة</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">نطاق الإيجار الشهري (ريال)</label>
                    <div class="row">
                        <div class="col-6">
                            <input type="number" class="form-control" name="min_rent"
                                   value="{{ request.args.get('min_rent', '') }}" placeholder="من">
                        </div>
                        <div class="col-6">
                            <input type="number" class="form-control" name="max_rent"
                                   value="{{ request.args.get('max_rent', '') }}" placeholder="إلى">
                        </div>
                    </div>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-12">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="fas fa-search me-1"></i>
                        بحث وتصفية
                    </button>
                    <a href="{{ url_for('leases_enhanced') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-1"></i>
                        مسح الفلاتر
                    </a>
                </div>
            </div>
        </form>
    </div>

    <!-- قائمة العقود -->
    <div class="row">
        {% if leases %}
            {% for lease in leases %}
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card lease-card">
                    <div class="lease-header">
                        <div class="lease-status
                            {% if lease.status == 'نشط' %}status-active
                            {% elif lease.status == 'منتهي' %}status-expired
                            {% elif lease.status == 'في الانتظار' %}status-pending
                            {% else %}status-terminated{% endif %}">
                            {{ lease.status }}
                        </div>

                        <div class="lease-icon">
                            <i class="fas fa-file-contract"></i>
                        </div>

                        <div class="lease-code">{{ lease.lease_code }}</div>
                        <div class="lease-property">{{ lease.property.name if lease.property else 'عقار غير محدد' }}</div>

                        {% if lease.is_expiring_soon() %}
                        <div class="expiry-warning mt-2">
                            <i class="fas fa-exclamation-triangle me-1"></i>
                            ينتهي قريباً
                        </div>
                        {% endif %}
                    </div>

                    <div class="lease-info">
                        <!-- معلومات المستأجر -->
                        <div class="tenant-info">
                            <div class="tenant-name">
                                <i class="fas fa-user me-2"></i>
                                {{ lease.tenant.full_name if lease.tenant else 'مستأجر غير محدد' }}
                            </div>
                            <div class="tenant-contact">
                                <i class="fas fa-phone me-1"></i>
                                {{ lease.tenant.phone_number if lease.tenant and lease.tenant.phone_number else 'غير محدد' }}
                            </div>
                        </div>

                        <!-- الملخص المالي -->
                        <div class="financial-summary">
                            <div class="financial-amount">
                                {{ lease.monthly_rent|number_format }} {{ lease.currency or 'ريال' }}
                            </div>
                            <div class="financial-label">
                                <i class="fas fa-money-bill-wave me-1"></i>
                                الإيجار الشهري
                            </div>
                        </div>

                        <div class="info-row">
                            <div class="info-label">
                                <i class="fas fa-calendar-alt"></i>
                                تاريخ البداية
                            </div>
                            <div class="info-value">{{ lease.start_date.strftime('%Y-%m-%d') if lease.start_date else 'غير محدد' }}</div>
                        </div>

                        <div class="info-row">
                            <div class="info-label">
                                <i class="fas fa-calendar-times"></i>
                                تاريخ الانتهاء
                            </div>
                            <div class="info-value">{{ lease.end_date.strftime('%Y-%m-%d') if lease.end_date else 'غير محدد' }}</div>
                        </div>
                        
                        <div class="info-row">
                            <div class="info-label">
                                <i class="fas fa-clock"></i>
                                مدة العقد
                            </div>
                            <div class="info-value">{{ lease.duration_months or 0 }} شهر</div>
                        </div>
                        
                        <!-- شريط التقدم -->
                        <div class="lease-progress">
                            {% set progress = lease.get_progress_percentage() %}
                            <div class="progress-label">
                                <span>تقدم العقد</span>
                                <span>{{ progress }}%</span>
                            </div>
                            <div class="progress">
                                <div class="progress-bar 
                                    {% if progress < 70 %}progress-active
                                    {% elif progress < 90 %}progress-warning
                                    {% else %}progress-danger{% endif %}" 
                                    style="width: {{ progress }}%"></div>
                            </div>
                        </div>
                        
                        <div class="info-row">
                            <div class="info-label">
                                <i class="fas fa-credit-card"></i>
                                حالة الدفع
                            </div>
                            <div class="info-value">
                                {% set payment_status = lease.get_payment_status() %}
                                <span class="payment-indicator 
                                    {% if payment_status == 'محدث' %}payment-current
                                    {% elif payment_status == 'متأخر' %}payment-late
                                    {% else %}payment-upcoming{% endif %}">
                                    <i class="fas fa-circle" style="font-size: 0.5rem;"></i>
                                    {{ payment_status }}
                                </span>
                            </div>
                        </div>
                        
                        <div class="quick-stats">
                            <div class="quick-stat">
                                <div class="quick-stat-number">{{ lease.installments.count() }}</div>
                                <div class="quick-stat-label">أقساط</div>
                            </div>
                            <div class="quick-stat">
                                <div class="quick-stat-number">{{ lease.payments.count() }}</div>
                                <div class="quick-stat-label">مدفوعات</div>
                            </div>
                            <div class="quick-stat">
                                <div class="quick-stat-number">{{ lease.documents.count() }}</div>
                                <div class="quick-stat-label">مستندات</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="lease-actions">
                        <a href="{{ url_for('lease_details', id=lease.id) }}" class="btn btn-action btn-view">
                            <i class="fas fa-eye me-1"></i>
                            عرض
                        </a>
                        <a href="{{ url_for('edit_lease', id=lease.id) }}" class="btn btn-action btn-edit">
                            <i class="fas fa-edit me-1"></i>
                            تعديل
                        </a>
                        {% if lease.status == 'نشط' and lease.is_expiring_soon() %}
                        <button class="btn btn-action btn-renew" onclick="renewLease({{ lease.id }}, '{{ lease.lease_code }}')">
                            <i class="fas fa-redo me-1"></i>
                            تجديد
                        </button>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% endfor %}
        {% else %}
            <div class="col-12">
                <div class="empty-state">
                    <i class="fas fa-file-contract"></i>
                    <h3>لا توجد عقود إيجار</h3>
                    <p>لم يتم العثور على عقود تطابق معايير البحث المحددة</p>
                    <button class="btn btn-primary btn-lg" data-bs-toggle="modal" data-bs-target="#addLeaseModal">
                        <i class="fas fa-plus me-2"></i>
                        إضافة عقد جديد
                    </button>
                </div>
            </div>
        {% endif %}
    </div>
</div>

<!-- زر الإضافة العائم -->
<button class="floating-add-btn" data-bs-toggle="modal" data-bs-target="#addLeaseModal">
    <i class="fas fa-plus"></i>
</button>

<!-- تضمين مودال إضافة عقد -->
{% include 'leases/modals/add_lease_modal.html' %}
{% endblock %}

{% block extra_js %}
<script>
// تأكيد الحذف
function confirmDelete(leaseId, leaseCode) {
    if (confirm(`هل أنت متأكد من حذف العقد "${leaseCode}"؟\nهذا الإجراء لا يمكن التراجع عنه.`)) {
        fetch(`/leases/${leaseId}/delete`, {
            method: 'DELETE',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrf_token]').value,
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('حدث خطأ أثناء حذف العقد');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء حذف العقد');
        });
    }
}

// تجديد العقد
function renewLease(leaseId, leaseCode) {
    if (confirm(`هل تريد تجديد العقد "${leaseCode}"؟`)) {
        // يمكن تطوير هذه الوظيفة لفتح نافذة تجديد العقد
        alert(`سيتم فتح نافذة تجديد العقد ${leaseCode} قريباً`);
    }
}

// تحديث البحث التلقائي
document.addEventListener('DOMContentLoaded', function() {
    console.log('✅ Leases Enhanced JavaScript loaded successfully');
    const filterForm = document.getElementById('filterForm');
    if (filterForm) {
        filterForm.addEventListener('change', function() {
            this.submit();
        });
    }

    // إضافة تأثيرات التحميل للبطاقات
    const leaseCards = document.querySelectorAll('.lease-card');
    leaseCards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
        card.classList.add('animate-fade-in');
    });

    // تحسين تجربة المستخدم للمودال
    const addLeaseModal = document.getElementById('addLeaseModal');
    if (addLeaseModal) {
        addLeaseModal.addEventListener('show.bs.modal', function() {
            setTimeout(() => {
                const firstInput = this.querySelector('input[type="text"]');
                if (firstInput) firstInput.focus();
            }, 500);
        });
    }

    // تحديث الإحصائيات في الوقت الفعلي
    updateLeaseStats();
});

// تحديث إحصائيات العقود
function updateLeaseStats() {
    fetch('/api/leases/stats')
        .then(response => response.json())
        .then(data => {
            const statNumbers = document.querySelectorAll('.stat-number');
            if (statNumbers.length >= 4) {
                statNumbers[0].textContent = data.total || 0;
                statNumbers[1].textContent = data.active || 0;
                statNumbers[2].textContent = data.expiring_soon || 0;
                statNumbers[3].textContent = data.expired || 0;
            }
        })
        .catch(error => console.error('Error updating lease stats:', error));
}

// تحديث الإحصائيات كل دقيقة
setInterval(updateLeaseStats, 60000);

// إضافة تأثيرات بصرية للأزرار
document.addEventListener('DOMContentLoaded', function() {
    const actionButtons = document.querySelectorAll('.btn-action');
    actionButtons.forEach(button => {
        button.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
        });

        button.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });

    // تحسين تجربة المستخدم للفلاتر
    const filterInputs = document.querySelectorAll('.search-filters input, .search-filters select');
    filterInputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.style.transform = 'scale(1.02)';
        });

        input.addEventListener('blur', function() {
            this.parentElement.style.transform = 'scale(1)';
        });
    });

    // تحديث أشرطة التقدم بشكل ديناميكي
    const progressBars = document.querySelectorAll('.progress-bar');
    progressBars.forEach(bar => {
        const width = bar.style.width;
        bar.style.width = '0%';
        setTimeout(() => {
            bar.style.width = width;
        }, 500);
    });
});

// تصدير البيانات
function exportLeases(format) {
    const params = new URLSearchParams(window.location.search);
    params.set('export', format);
    window.location.href = `/leases/export?${params.toString()}`;
}

// طباعة العقد
function printLease(leaseId) {
    window.open(`/leases/${leaseId}/print`, '_blank');
}
</script>
{% endblock %}
