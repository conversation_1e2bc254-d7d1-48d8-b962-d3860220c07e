#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
from app import app, db
from app.models import User
from werkzeug.security import generate_password_hash
from datetime import datetime

def create_default_user():
    """إنشاء مستخدم افتراضي إذا لم يوجد"""
    try:
        with app.app_context():
            if User.query.count() == 0:
                admin_user = User(
                    username='admin',
                    password=generate_password_hash('admin123'),
                    email='<EMAIL>',
                    full_name='مدير النظام',
                    role='admin',
                    is_active=True,
                    created_at=datetime.now()
                )
                db.session.add(admin_user)
                db.session.commit()
                print("✅ تم إنشاء المستخدم الافتراضي:")
                print("   اسم المستخدم: admin")
                print("   كلمة المرور: admin123")
    except Exception as e:
        print(f"⚠️ تحذير: {e}")

if __name__ == "__main__":
    print("بدء تشغيل نظام إدارة المكتب...")

    # إنشاء مستخدم افتراضي
    create_default_user()

    print("الخادم يعمل على: http://localhost:5000")
    print("صفحة تسجيل الدخول: http://localhost:5000/lawyersameh")
    print("=" * 50)

    app.run(debug=True, host='0.0.0.0', port=5000)
